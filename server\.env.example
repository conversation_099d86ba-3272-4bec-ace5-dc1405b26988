# Node.js Development Environment
PORT=8787
NODE_ENV=development

# Database Configuration
# For local development, embedded-postgres is used by default
# For production deployment, you need a standalone PostgreSQL database
DATABASE_URL={{DATABASE_URL}}

# Firebase Configuration (J<PERSON><PERSON> approach)
FIREBASE_PROJECT_ID={{FIREBASE_PROJECT_ID}}

# Cloudflare Configuration
WORKER_NAME={{WORKER_NAME}} 