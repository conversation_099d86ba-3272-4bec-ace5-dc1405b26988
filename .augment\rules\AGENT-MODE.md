---
type: "always_apply"
---

Of course. Here is a revised version of the Agent Mode Protocol that incorporates the detailed workflow for complex features, presented as a new "Deep Task Mode."

***

# Agent Mode Protocol

This document outlines how to activate and use different agent modes to handle specific tasks. When you invoke an agent, I adopt its unique skills, workflow, and output style.

---

## Agent Activation

There are three primary ways to activate an agent, depending on the complexity of your task.

### 1. Simple Tasks: Automatic Selection
For straightforward tasks, use the `/agent` command. I will automatically select the best agent based on the context.
* **Command**: `/agent`

### 2. Custom Workflows: Multi-Agent Mode
For tasks requiring a custom sequence of specialists, use the `/multiagent` command or chain agents explicitly.
* **Command**: `/multiagent`
* **Example**: `Use @agents-agument/core/prompt-assistant.md, then @agents-agument/universal/backend-developer.md to implement.`

### 3. Complex Features: Deep Task Mode
For complicated features requiring a structured, multi-phase workflow, use the `/deeptask` command. This mode follows a specific, five-step artistic workflow to ensure cohesive development from planning to integration.

* **Command**: `/deeptask`

---

## Deep Task Workflow

When you initiate `/deeptask`, I will coordinate a team of agents through the following five phases:

**Phase 1: Planning**
* **Goal**: Define the feature, requirements, and technical approach.
* **Primary Agent**: `@agents-agument/core/project-researcher-agent`

**Phase 2: Data Layer Implementation**
* **Goal**: Implement all necessary database schemas, models, and data access logic.
* **Primary Agent**: `@agents-agument/universal/backend-developer`

**Phase 3: Parallel Development**
* **Goal**: Build the backend and frontend components simultaneously.
* **Agents**:
    * `@agents-agument/universal/backend-developer` (for server-side logic and APIs)
    * `@agents-agument/universal/frontend-developer` (for UI components and client-side logic)

**Phase 4: Phased Code Review**
* **Goal**: Review the backend and frontend code separately to ensure quality and security before integration.
* **Primary Agent**: `@agents-agument/core/code-reviewer`

**Phase 5: Integration & Final Review**
* **Goal**: Combine the backend and frontend, ensure they work together seamlessly, and conduct a final review.
* **Agents**:
    * `@agents-agument/universal/backend-developer`
    * `@agents-agument/universal/frontend-developer`
    * `@agents-agument/core/code-reviewer`

---

## Available Agents

Below is a reference of available agents for manual activation or custom workflows.

### Core Agents (`agents-agument/core/`)
* **prompt-assistant**: Generates implementation-ready prompts.
* **code-reviewer**: Performs security-aware code reviews.
* **documentation-specialist**: Creates and maintains technical documentation.
* **performance-optimizer**: Analyzes and improves code performance.
* **project-researcher-agent**: Conducts project planning and tech stack research.
* **ui-configurator-agent**: Assists with interactive UI design.

### Universal & Specialized Agents
* **universal/backend-developer**: For any server-side language.
* **universal/frontend-developer**: For any client-side development.
* **universal/api-architect**: For API design and architecture.
* **specialized/**: Contains agents for **React**, **Vue**, **Django**, etc.

### External Agents (`agents-agument/ClaudeCodeAgents-master/`)
* **Jenny**: Senior developer persona for in-depth code analysis.
* **karen**: Strict persona for enforcing code quality.
* **task-completion-validator**: Validates if task criteria are met.