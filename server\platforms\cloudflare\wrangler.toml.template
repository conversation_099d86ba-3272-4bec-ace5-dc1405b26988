name = "{{WORKER_NAME}}"
main = "src/api.ts"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]

[vars]
RUNTIME = "cloudflare"
FIREBASE_PROJECT_ID = "{{FIREBASE_PROJECT_ID}}"
DATABASE_URL = "{{DATABASE_URL}}"
# FIREBASE_AUTH_EMULATOR_HOST will be dynamically set when using emulator

# Development tools configuration
[dev]
local_protocol = "http"
port = 8787  # Will be dynamically updated by port-manager.js 